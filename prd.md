PRD – Pixel and Code Portfolio Website

Tech Stack: <PERSON><PERSON> 12 (Backend + Blade), Tailwind CSS 4+, Alpine.js (for interactivity), MySQL (Database)
Purpose: Showcase <PERSON>’s 2D/3D graphics, web development skills, projects, blogs, and allow dynamic content management via an admin panel.

1.  Core Features
    Navigation (Global)

        Navbar on all pages:

            Menu Items: Home, About Me, Projects, Video/Image Gallery, Blog, Contact Me.

            Active state highlighting for the current page.

            Sticky navbar with smooth scroll.

            Responsive design (hamburger menu on mobile).

2.  Home Page Sections
    Hero Section

        Displays:

            Name: <PERSON>

            Short description: "2D/3D Graphics & Web Developer"

            Call-to-Actions (CTAs): View Projects (scrolls to Projects) and Let’s Get Started (jumps to Contact).

            Background gradient with subtle particle animation (Alpine.js or CSS-based, not resource-heavy).

Featured Projects

    Section shows 6 projects:

        Each Project Card Includes:

            Eye-catching thumbnail (GIF, video loop, or image).

            Title and 1-sentence description.

            Hover Effects (zoom or fade).

            Tech stack icons (<PERSON><PERSON>, Tai<PERSON><PERSON>, React, etc.).

            Button: Live Demo.

        "View All Projects" button linking to /projects.

    Projects are fully CRUD-enabled via admin panel.

Skills / Technologies (SEO & ATS Optimized)

    Categorized into:

        Frontend, Backend, DevOps.

    Each skill:

        Colorful free icon (SVG).

        Progress bar (animated).

        Metadata for SEO (schema tags).

    Skills editable via Admin Panel (CRUD).

Featured Blog Posts

    Display 6 recent posts with:

        Thumbnail.

        Title + short excerpt.

        "Read More" button.

    "View All Posts" button linking to /blog.

    Fully managed via Admin Panel (CRUD).

Trending Tech News

    Uses Free Tech News API (e.g., NewsAPI.org API) api keys are available in api.txt file.

    On each page load:

        Fetch 6 trending articles.

        Display as cards (image, headline, link to source).

    Refresh dynamically (API fetch on reload).

    Fully CRUD-enabled via Admin Panel only the number of articles should be able to set from the backend.

Client Testimonials

    Testimonial Carousel:

        Smooth horizontal sliding animation (Alpine.js or Swiper.js).

        Each card includes:

            Client name + company (optional).

            Testimonial text.

            Star rating (if available).

    Fully CRUD-enabled via Admin Panel.

Footer

    Contains:

        Short description of Aziz Khan.

        Links to: Home, About, Projects, Blog, Contact.

        Copyright:

        © {auto-updating-year} Pixel and Code. All rights reserved. Developed by Aziz Khan.

3.  Standalone Pages

    Each menu link (About Me, Projects, Video/Image Gallery, Blog, Contact Me) has its own full page.

    Projects page shows all projects with filters (e.g., by tech stack).

    Video/Image Gallery uses:

        Lightbox with lazy loading for performance.

        Filter by category (videos/images).

    Blog page:

        Paginated list.

        Single post page with related posts.

    Contact Me page:

        Contact form with:

            Name, Email, Subject, Message.

            Sends email (Laravel Mail).

            Spam protection (Honeypot or reCAPTCHA).

4.  Admin Panel (CRUD)

    Laravel Nova/Filament or custom-built.

    Admin can manage:

        Projects (create, edit, delete, upload thumbnails, tech stack).

        Blog posts (WYSIWYG editor).

        Skills (icons, progress levels).

        Testimonials (carousel items).

        Video/Image Gallery (upload, categorize).

        Navbar menus (order, add/remove items).

        Footer text and links.

        All front page sections should have a option to show or hide button.

    Admin authentication with role-based access.

5.  Performance & SEO

    TailwindCSS 4+ JIT for optimized builds.

    Lazy-loading for images and videos.

6.  Use images from the public/images directory.
